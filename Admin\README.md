# 🎛️ Notiflair Admin

> Interface d'administration moderne pour la gestion des notifications Notiflair

## 📋 Vue d'ensemble

Interface d'administration développée avec **Vite + React + Tailwind CSS** pour gérer les notifications, templates et paramètres de la plateforme Notiflair.

## 🚀 Fonctionnalités

### ✅ Implémentées

- **🔐 Authentification intégrée** avec l'API FastAPI (JWT)
- **📊 Dashboard** avec statistiques et vue d'ensemble
- **🔔 Gestion des notifications** avec templates personnalisables
- **⚙️ Configuration des canaux** (Push, In-App, Email)
- **⏰ Paramétrage des timings** de rappel
- **📱 Interface responsive** adaptée mobile/desktop
- **🎨 Design moderne** avec animations et transitions
- **🔗 Intégration API** complète avec gestion d'erreurs

### 🎯 Fonctionnalités clés

- **Authentification JWT** : Tokens sécurisés avec expiration automatique
- **Templates de notifications** : 4 types (Info, Urgent, Manqué, Complété)
- **Canaux multiples** : Push, In-App, Email avec statistiques d'usage
- **Timings configurables** : 5min, 15min, 1h, 1j, 1sem + personnalisé
- **Navigation intuitive** avec sidebar responsive et états actifs
- **Notifications toast** pour les retours utilisateur
- **Protection des routes** avec vérification en temps réel
- **Gestion des sessions** : Déconnexion automatique à l'expiration
- **Gestion des traductions** : Interface pour traductions de base par langue et overrides par pays-langue

## 🛠️ Technologies

- **Frontend** : React 18 + Vite
- **Styling** : Tailwind CSS 3
- **Routing** : React Router v6
- **Icons** : Heroicons (SVG)
- **Fonts** : Inter (Google Fonts)

## 🌐 Gestion des traductions

L'interface admin permet de gérer les traductions des types de notifications selon la nouvelle architecture 2025 :

### Architecture des traductions

1. **Traductions de base (par langue)** : Traductions universelles utilisées par défaut

   - Une traduction par langue (fr, de, it, nl)
   - Stockées dans `t_ntp_translations_ntt` avec `lng_id`
   - Affichées dans la section "Traductions de base"

2. **Overrides spécifiques (par pays-langue)** : Traductions qui remplacent les traductions de base
   - Une traduction par combinaison pays-langue (ex: BE-fr, CH-de)
   - Stockées dans `t_ntp_translations_override_nto` avec `ctl_id`
   - Affichées dans la section "Overrides spécifiques"

### Interface utilisateur

- **Tableau unifié** : Une seule table avec colonnes Langue, Label, Description, Overrides
- **Colonne Overrides** : Interface intuitive pour gérer les spécificités pays-langue
  - Bouton "+ Ajouter pays" pour créer de nouveaux overrides
  - Dropdown avec drapeaux et recherche pour sélectionner un pays
  - Badges colorés pour visualiser les overrides existants
  - Bouton de suppression (✕) pour chaque override

### Fonctionnalités UX

- **Recherche de pays** : Filtre en temps réel par nom ou code pays
- **Drapeaux visuels** : Identification rapide des pays avec émojis
- **Fermeture automatique** : Clic en dehors pour fermer les dropdowns
- **Indicateurs visuels** :
  - Lignes grises pour les traductions manquantes
  - Badges orange pour les overrides existants
  - Focus automatique sur le champ de recherche

### Flux de données

1. L'API retourne les traductions avec les flags `has_base_translation` et `has_override`
2. L'interface affiche tout dans un tableau unifié
3. Les overrides sont gérés directement dans la colonne dédiée
4. Création/suppression d'overrides en temps réel

## 🚀 Installation et démarrage

### Prérequis

- Node.js 16+
- npm ou yarn

### Installation

```bash
# Depuis le dossier racine Notiflair
cd Admin

# Installer les dépendances
npm install

# Lancer en mode développement
npm run dev

# L'application sera disponible sur http://localhost:5173
```

### Build de production

```bash
# Construire pour la production
npm run build

# Prévisualiser le build
npm run preview
```

## 🔐 Authentification

### Intégration API

L'authentification est maintenant **entièrement intégrée** avec l'API FastAPI :

- **JWT Tokens** : Authentification sécurisée avec tokens d'expiration
- **Protection des routes** : Vérification automatique des sessions
- **Gestion des erreurs** : Messages contextuels de l'API
- **Déconnexion propre** : Invalidation des tokens côté serveur

### Identifiants de test

Utilisez les identifiants configurés dans la base de données MySQL :

- **Username** : `admin`
- **Password** : `secret`

> Les identifiants dépendent de la configuration de votre base de données. Consultez l'API sur http://localhost:8000/docs pour plus d'informations.

## 📁 Structure du projet

```
Admin/
├── public/                 # Fichiers statiques
├── src/
│   ├── components/         # Composants React
│   │   ├── Login.jsx      # Page de connexion
│   │   ├── Layout.jsx     # Layout principal avec sidebar
│   │   ├── Dashboard.jsx  # Page d'accueil
│   │   ├── Notifications.jsx # Gestion des notifications
│   │   └── ProtectedRoute.jsx # Protection des routes
│   ├── index.css          # Styles Tailwind + personnalisés
│   ├── App.jsx            # Composant racine avec routing
│   └── main.jsx           # Point d'entrée
├── index.html             # Template HTML
├── tailwind.config.js     # Configuration Tailwind
├── postcss.config.js      # Configuration PostCSS
├── vite.config.js         # Configuration Vite
└── package.json           # Dépendances et scripts
```

## 🎨 Design System

### Couleurs principales

- **Primary** : Blue-600 (#2563eb)
- **Success** : Green-600 (#059669)
- **Warning** : Amber-600 (#d97706)
- **Error** : Red-600 (#dc2626)
- **Background** : Gray-50 (#f9fafb)

### Composants

- **Cards** : Arrondies avec ombres subtiles
- **Buttons** : États hover/focus avec transitions
- **Forms** : Inputs avec focus personnalisé
- **Navigation** : Sidebar responsive avec états actifs
- **Notifications** : Toast avec animations slide-in

## 🔄 Intégration API

### ✅ Intégration complète

L'interface est maintenant **entièrement intégrée** avec l'API FastAPI :

- **Authentification JWT** : Connexion sécurisée avec tokens d'expiration
- **Services API** : Classes dédiées pour les appels HTTP
- **Gestion d'erreurs** : Messages contextuels et retry automatique
- **Contexte React** : État global d'authentification
- **Protection des routes** : Vérification automatique des sessions

### 🔧 Configuration

```bash
# Variables d'environnement (.env)
VITE_API_URL=http://localhost:8000
VITE_APP_VERSION=2.1.0
```

### Fonctionnalités à venir

- **Gestion des utilisateurs** et permissions
- **Statistiques avancées** avec graphiques
- **Éditeur de templates** WYSIWYG
- **Planification** de notifications
- **Logs et audit** des actions

## 🧪 Tests

```bash
# Lancer les tests (à implémenter)
npm run test

# Tests avec coverage
npm run test:coverage
```

## 📦 Déploiement

### Build optimisé

```bash
npm run build
# Génère le dossier dist/ prêt pour la production
```

### Variables d'environnement

Créer un fichier `.env` :

```env
VITE_API_URL=http://localhost:8000
VITE_APP_VERSION=2.1.0
```

## 🤝 Contribution

1. Respecter la structure des composants
2. Utiliser Tailwind pour le styling
3. Maintenir la cohérence du design system
4. Tester sur mobile et desktop

## 📄 Licence

Partie du projet Notiflair - Voir licence principale

---

## 📊 Status

| Fonctionnalité       | Status              | Notes                          |
| -------------------- | ------------------- | ------------------------------ |
| **Authentification** | ✅ **Intégrée**     | JWT avec API FastAPI           |
| **Dashboard**        | ✅ **Opérationnel** | Statistiques et vue d'ensemble |
| **Templates**        | ✅ **Opérationnel** | 4 types configurables          |
| **Canaux**           | ✅ **Opérationnel** | Push, In-App, Email            |
| **Timings**          | ✅ **Opérationnel** | Configurables + personnalisé   |
| **Responsive**       | ✅ **Opérationnel** | Mobile et desktop              |
| **API Integration**  | ✅ **Complète**     | Services + contexte React      |

**Status global** : ✅ **Version 2.1.0 - Intégration API complète**
