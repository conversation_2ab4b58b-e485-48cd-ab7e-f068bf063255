import { useEffect, useState } from 'react';

// Composant pour afficher les icônes Ionicons avec couleur
const IonIcon = ({ name, color, size = '20px', className = '' }) => {
  useEffect(() => {
    // Charger les icônes Ionicons dynamiquement
    const script = document.createElement('script');
    script.type = 'module';
    script.src = 'https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js';
    document.head.appendChild(script);

    const nomoduleScript = document.createElement('script');
    nomoduleScript.noModule = true;
    nomoduleScript.src = 'https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js';
    document.head.appendChild(nomoduleScript);

    return () => {
      // Cleanup si nécessaire
    };
  }, []);

  return (
    <ion-icon
      name={name}
      style={{
        color: color,
        fontSize: size,
        '--ionicon-stroke-width': '32px'
      }}
      className={className}
    ></ion-icon>
  );
};

const Events = () => {
  const [selectedNode, setSelectedNode] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const [hierarchyData, setHierarchyData] = useState({ types: [], stats: {} });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('FR');
  const [selectedLanguage, setSelectedLanguage] = useState('fr');

  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const showDetails = (type, id) => {
    // Ne montrer le panneau de détails que pour les types (pas les notifications)
    if (type === 'type') {
      setSelectedNode({ type, id });
      setShowDetailPanel(true);
    } else {
      // Pour les notifications, on ne fait rien pour l'instant
      setSelectedNode({ type, id });
      setShowDetailPanel(false);
    }
  };

  const closeDetailPanel = () => {
    setShowDetailPanel(false);
    setSelectedNode(null);
  };

  // Chargement des données depuis l'API
  const loadHierarchyData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement des données depuis l\'API...');

      const url = `http://localhost:8000/api/v1/notifications/hierarchy?country_code=${selectedCountry}&language_code=${selectedLanguage}`;
      console.log('📡 URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors'
      });

      console.log('📊 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erreur API ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Données reçues:', data);

      // Transformer les données pour le composant
      const transformedData = transformHierarchyData(data);
      setHierarchyData(transformedData);

    } catch (err) {
      console.error('❌ Erreur lors du chargement des données:', err);

      // Gestion spécifique des erreurs de réseau
      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        setError('Impossible de se connecter à l\'API. Vérifiez que le serveur backend est démarré sur le port 8000.');
      } else {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // Transformation des données API
  const transformHierarchyData = (apiData) => {
    if (!apiData || !apiData.hierarchy) {
      return { types: [], stats: {} };
    }

    const stats = {
      totalTypes: apiData.total_types || 0,
      totalNotifications: apiData.total_notifications || 0,
      activeNotifications: apiData.active_notifications || 0,
      countryCode: apiData.country_code,
      languageCode: apiData.language_code
    };

    const types = apiData.hierarchy.map(item => transformHierarchyItem(item));
    return { types, stats };
  };

  // Transformation d'un élément de hiérarchie
  const transformHierarchyItem = (item) => {
    const type = item.notification_type;
    const notifications = item.notifications || [];
    const children = (item.children || []).map(child => transformHierarchyItem(child));

    return {
      id: type.ntp_id,
      key: type.ntp_identifier_key,
      label: type.label || type.ntp_identifier_key,
      description: type.description,
      icon: type.ntp_icon,
      color: type.ntp_color,
      bgColor: type.ntp_bg_color,
      level: type.ntp_level,
      parentId: type.ntp_parent_id,
      displayOrder: type.ntp_display_order,
      defaultEnabled: type.ntp_default_enabled,
      translationSource: type.translation_source,
      notifications: notifications.map(notif => ({
        id: notif.ntf_id,
        key: notif.ntf_identifier_key,
        label: notif.label || notif.ntf_identifier_key,
        description: notif.description,
        isActive: notif.ntf_is_active,
        icon: notif.ntf_icon,
        usageCount: notif.ntf_usage_count || 0,
        relevanceScore: notif.ntf_relevance_score,
        countryCode: notif.country_code,
        countryName: notif.country_name,
        translationSource: notif.translation_source,
        rrule: notif.ntf_rrule,
        rruleText: notif.ntf_rrule_text
      })),
      children: children,
      totalNotifications: notifications.length + children.reduce((sum, child) => sum + child.totalNotifications, 0)
    };
  };

  // Chargement initial des données avec délai
  useEffect(() => {
    // Délai pour laisser le temps à l'API de démarrer
    const timer = setTimeout(() => {
      loadHierarchyData();
    }, 1000);

    return () => clearTimeout(timer);
  }, [selectedCountry, selectedLanguage]);

  // Filtrage des types selon la recherche
  const filteredTypes = hierarchyData.types.filter(type => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return type.label.toLowerCase().includes(searchLower) ||
           type.notifications.some(notif => notif.label.toLowerCase().includes(searchLower));
  });

  return (
    <div className="h-full flex overflow-hidden -m-6">
      {/* Tree View */}
      <div className="w-full lg:w-1/2 xl:w-2/5 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-6">
          {/* Search and filters */}
          <div className="mb-6">
            <div className="relative">
              <input 
                type="text" 
                placeholder="Rechercher un événement..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            
            {/* Statistiques */}
            {hierarchyData.stats.totalTypes > 0 && (
              <div className="mt-3 text-sm text-gray-600">
                {hierarchyData.stats.totalTypes} types • {hierarchyData.stats.totalNotifications} événements
                {hierarchyData.stats.countryCode && (
                  <span className="ml-2">• {hierarchyData.stats.countryCode}/{hierarchyData.stats.languageCode}</span>
                )}
              </div>
            )}
          </div>

          {/* Loading state */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-3 text-gray-600">Chargement des événements...</span>
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Erreur de chargement</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                  <div className="mt-3 space-x-3">
                    <button
                      onClick={loadHierarchyData}
                      className="text-sm text-red-600 hover:text-red-500 underline"
                    >
                      Réessayer
                    </button>
                    <button
                      onClick={() => {
                        console.log('🧪 Test de connexion API...');
                        fetch('http://localhost:8000/')
                          .then(response => response.json())
                          .then(data => {
                            console.log('✅ API accessible:', data);
                            alert('API accessible ! Problème probablement avec l\'endpoint hierarchy.');
                          })
                          .catch(err => {
                            console.error('❌ API non accessible:', err);
                            alert('API non accessible. Vérifiez que le serveur backend est démarré.');
                          });
                      }}
                      className="text-sm text-blue-600 hover:text-blue-500 underline"
                    >
                      Tester API
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Tree Structure */}
          {!loading && !error && (
            <div className="space-y-1">
              {filteredTypes.map(type => (
                <TypeTreeNode 
                  key={type.id}
                  type={type}
                  selectedNode={selectedNode}
                  expandedNodes={expandedNodes}
                  onToggleNode={toggleNode}
                  onShowDetails={showDetails}
                />
              ))}
              
              {filteredTypes.length === 0 && searchTerm && (
                <div className="text-center py-8 text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <p>Aucun événement trouvé pour "{searchTerm}"</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Detail Panel */}
      <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-white">
        <div className="flex-1 flex flex-col">
          {!selectedNode || selectedNode.type !== 'type' ? (
            /* Empty State */
            <div className="flex-1 flex items-center justify-center p-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Sélectionnez un type d'événement</h3>
                <p className="text-gray-500">Cliquez sur un type d'événement dans l'arbre pour voir ses détails et le modifier</p>
              </div>
            </div>
          ) : (
            /* Panel Content pour les types */
            <TypeDetailsPanel
              typeId={selectedNode.id}
              hierarchyData={hierarchyData}
              onClose={closeDetailPanel}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher un nœud de type dans l'arbre
const TypeTreeNode = ({ type, selectedNode, expandedNodes, onToggleNode, onShowDetails }) => {
  const isExpanded = expandedNodes.has(type.key);
  const isSelected = selectedNode?.type === 'type' && selectedNode?.id === type.id;
  const hasChildren = type.children.length > 0 || type.notifications.length > 0;

  // Couleurs par défaut si pas définies
  const bgColor = type.bgColor || '#f3f4f6';
  const color = type.color || '#6b7280';

  return (
    <div>
      {/* Nœud principal du type */}
      <div
        className={`tree-node flex items-center p-3 rounded-lg cursor-pointer ${isSelected ? 'active' : ''}`}
        onClick={() => onShowDetails('type', type.id)}
      >
        {hasChildren && (
          <svg
            className={`w-4 h-4 text-gray-400 mr-2 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            onClick={(e) => {
              e.stopPropagation();
              onToggleNode(type.key);
            }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
          </svg>
        )}

        <div
          className="w-8 h-8 rounded-lg flex items-center justify-center mr-3"
          style={{ backgroundColor: bgColor }}
        >
          {type.icon ? (
            <IonIcon
              name={type.icon}
              color={color}
              size="20px"
              className="flex items-center justify-center"
            />
          ) : (
            <IonIcon
              name="folder-outline"
              color={color}
              size="20px"
              className="flex items-center justify-center"
            />
          )}
        </div>

        <div className="flex-1">
          <div className="flex items-center">
            <span className="font-medium text-gray-900">{type.label}</span>
            <span className="ml-2 text-xs text-gray-500">
              ({type.children?.length || 0} sous-types - {type.notifications?.length || 0} notifications)
            </span>
          </div>
          <div className="flex items-center mt-1 space-x-2">
            <span className={`text-xs px-2 py-0.5 rounded ${type.defaultEnabled ? 'status-active' : 'status-inactive'}`}>
              {type.defaultEnabled ? 'Actif' : 'Inactif'}
            </span>
            <div className="flex space-x-1">
              <span className="w-2 h-2 bg-blue-500 rounded-full" title="France"></span>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications et sous-types */}
      {isExpanded && hasChildren && (
        <div className="ml-6 space-y-1 mt-1">
          {/* Notifications de ce type */}
          {type.notifications.map(notification => (
            <NotificationTreeNode
              key={notification.id}
              notification={notification}
              selectedNode={selectedNode}
              onShowDetails={onShowDetails}
            />
          ))}

          {/* Sous-types */}
          {type.children.map(childType => (
            <TypeTreeNode
              key={childType.id}
              type={childType}
              selectedNode={selectedNode}
              expandedNodes={expandedNodes}
              onToggleNode={onToggleNode}
              onShowDetails={onShowDetails}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Composant pour afficher une notification dans l'arbre
const NotificationTreeNode = ({ notification, selectedNode, onShowDetails }) => {
  const isSelected = selectedNode?.type === 'notification' && selectedNode?.id === notification.id;

  return (
    <div
      className={`tree-node flex items-center p-3 rounded-lg cursor-pointer ${isSelected ? 'active' : ''}`}
      onClick={() => onShowDetails('notification', notification.id)}
    >
      <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
        {notification.icon ? (
          <IonIcon
            name={notification.icon}
            color="#2563eb"
            size="16px"
            className="flex items-center justify-center"
          />
        ) : (
          <IonIcon
            name="notifications-outline"
            color="#2563eb"
            size="16px"
            className="flex items-center justify-center"
          />
        )}
      </div>
      <div className="flex-1">
        <div className="flex items-center">
          <span className="text-sm font-medium text-gray-900">{notification.label}</span>
          <span className="ml-2 text-xs text-gray-500">({notification.usageCount} notifications)</span>
        </div>
        <div className="flex items-center mt-1 space-x-2">
          <span className={`text-xs px-2 py-0.5 rounded ${notification.isActive ? 'status-active' : 'status-inactive'}`}>
            {notification.isActive ? 'Actif' : 'Inactif'}
          </span>
          {notification.countryCode && (
            <div className="flex space-x-1">
              <span className="w-2 h-2 bg-blue-500 rounded-full" title={notification.countryName}></span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};



// Composant pour le panneau de détails des types
const TypeDetailsPanel = ({ typeId, hierarchyData, onClose }) => {
  // Trouver le type dans les données
  const findTypeById = (types, id) => {
    for (const type of types) {
      if (type.id === id) return type;
      const found = findTypeById(type.children, id);
      if (found) return found;
    }
    return null;
  };

  const selectedType = findTypeById(hierarchyData.types, typeId);

  if (!selectedType) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-gray-500">Type d'événement non trouvé</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div className="flex items-center">
          <div
            className="w-10 h-10 rounded-lg flex items-center justify-center mr-3"
            style={{ backgroundColor: selectedType.bgColor || '#f3f4f6' }}
          >
            {selectedType.icon ? (
              <IonIcon
                name={selectedType.icon}
                color={selectedType.color || '#6b7280'}
                size="24px"
                className="flex items-center justify-center"
              />
            ) : (
              <IonIcon
                name="folder-outline"
                color={selectedType.color || '#6b7280'}
                size="24px"
                className="flex items-center justify-center"
              />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{selectedType.label}</h3>
            <p className="text-sm text-gray-500">Type d'événement • Niveau {selectedType.level}</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors lg:hidden"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      {/* Body */}
      <div className="flex-1 overflow-y-auto p-6 pb-20">
        <div className="space-y-6">
          {/* Informations générales et Statistiques côte à côte */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Informations générales */}
            <div>
              <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Informations générales</h4>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">ID système</span>
                  <span className="text-sm font-mono text-gray-900">{selectedType.key}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">ID base de données</span>
                  <span className="text-sm font-mono text-gray-900">{selectedType.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Niveau hiérarchique</span>
                  <span className="text-sm text-gray-900">{selectedType.level} {selectedType.level === 0 ? '(Racine)' : '(Enfant)'}</span>
                </div>
                {selectedType.parentId && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Type parent</span>
                    <span className="text-sm text-gray-900">ID {selectedType.parentId}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Ordre d'affichage</span>
                  <span className="text-sm text-gray-900">{selectedType.displayOrder || 'Non défini'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Source traduction</span>
                  <span className="text-sm text-gray-900">{selectedType.translationSource || 'default'}</span>
                </div>
              </div>
            </div>

            {/* Statistiques */}
            <div>
              <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Statistiques</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-blue-50 rounded-lg p-3">
                  <p className="text-xl font-bold text-blue-900">{selectedType.totalNotifications}</p>
                  <p className="text-xs text-blue-700">Événements totaux</p>
                </div>
                <div className="bg-green-50 rounded-lg p-3">
                  <p className="text-xl font-bold text-green-900">{selectedType.notifications.length}</p>
                  <p className="text-xs text-green-700">Événements directs</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-3">
                  <p className="text-xl font-bold text-purple-900">{selectedType.children.length}</p>
                  <p className="text-xs text-purple-700">Sous-types</p>
                </div>
                <div className="bg-orange-50 rounded-lg p-3">
                  <p className="text-xl font-bold text-orange-900">
                    {selectedType.notifications.filter(n => n.isActive).length}
                  </p>
                  <p className="text-xs text-orange-700">Événements actifs</p>
                </div>
              </div>
            </div>
          </div>

          {/* Configuration */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Configuration</h4>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="text-sm font-medium text-gray-700">État par défaut</label>
                  <p className="text-xs text-gray-500">Activer ce type par défaut pour les nouveaux utilisateurs</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    defaultChecked={selectedType.defaultEnabled}
                    disabled
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Apparence */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Apparence</h4>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Icône</label>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-8 h-8 rounded border border-gray-300 flex items-center justify-center"
                    style={{ backgroundColor: selectedType.bgColor || '#f3f4f6' }}
                  >
                    {selectedType.icon ? (
                      <IonIcon
                        name={selectedType.icon}
                        color={selectedType.color || '#6b7280'}
                        size="18px"
                      />
                    ) : (
                      <IonIcon
                        name="folder-outline"
                        color={selectedType.color || '#6b7280'}
                        size="18px"
                      />
                    )}
                  </div>
                  <input
                    type="text"
                    value={selectedType.icon || 'folder-outline'}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled
                  />
                </div>
                <a
                  href="https://ionic.io/ionicons"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:text-blue-800 underline mt-1 inline-block"
                >
                  📖 Voir la liste complète des icônes Ionicons
                </a>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Couleur principale</label>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-8 h-8 rounded border border-gray-300"
                    style={{ backgroundColor: selectedType.color || '#6b7280' }}
                  ></div>
                  <input
                    type="text"
                    value={selectedType.color || '#6b7280'}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Couleur de fond</label>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-8 h-8 rounded border border-gray-300"
                    style={{ backgroundColor: selectedType.bgColor || '#f3f4f6' }}
                  ></div>
                  <input
                    type="text"
                    value={selectedType.bgColor || '#f3f4f6'}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    disabled
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Traductions */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Traductions</h4>
            <TranslationTable
              selectedType={selectedType}
              hierarchyData={hierarchyData}
            />
          </div>


        </div>
      </div>

      {/* Actions - Barre sticky en bas */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4 flex justify-end space-x-3">
        <button
          type="button"
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Enregistrer les modifications
        </button>
      </div>
    </div>
  );
};

// Composant pour le tableau des traductions
const TranslationTable = ({ selectedType, hierarchyData }) => {
  const [translationsData, setTranslationsData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openCountrySelectors, setOpenCountrySelectors] = useState({});
  const [selectedOverrides, setSelectedOverrides] = useState({});
  const [countrySearchTerms, setCountrySearchTerms] = useState({});
  const [availableCountriesForLanguage, setAvailableCountriesForLanguage] = useState({});

  // Chargement des pays depuis l'API
  const loadCountriesForLanguages = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/reference/countries');

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();

      // Organiser les pays par langue
      const countriesByLanguage = {};

      data.countries.forEach(country => {
        country.languages.forEach(language => {
          if (!countriesByLanguage[language.lng_code]) {
            countriesByLanguage[language.lng_code] = [];
          }

          // Ajouter le pays avec le drapeau
          countriesByLanguage[language.lng_code].push({
            code: country.cty_code,
            name: country.cty_name,
            flag: getCountryFlag(country.cty_code)
          });
        });
      });

      setAvailableCountriesForLanguage(countriesByLanguage);

    } catch (err) {
      console.error('Erreur lors du chargement des pays:', err);
    }
  };

  const getCountryFlag = (countryCode) => {
    const flags = {
      'FR': '🇫🇷',
      'BE': '🇧🇪',
      'CH': '🇨🇭',
      'LU': '🇱🇺',
      'CA': '🇨🇦',
      'MC': '🇲🇨'
    };
    return flags[countryCode] || '🏳️';
  };

  // Chargement des traductions depuis l'API
  const loadTranslations = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Chargement des traductions pour le type:', selectedType.id);

      const response = await fetch(`http://localhost:8000/api/v1/notifications/types/${selectedType.id}/translations`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`Erreur API ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Traductions reçues:', data);

      // Organiser les données par langue uniquement (nouvelle architecture)
      const organized = {
        baseTranslations: {},
        overrides: {}
      };

      // Récupérer toutes les langues disponibles
      const allLanguages = ['fr', 'de', 'it', 'nl']; // Langues supportées

      // Initialiser la structure pour toutes les langues
      allLanguages.forEach(langCode => {
        organized.baseTranslations[langCode] = {
          label: '',
          description: '',
          source: 'empty',
          hasTranslation: false
        };
      });

      // Remplir avec les données existantes
      data.translations?.forEach(translation => {
        const languageCode = translation.language_code;
        const countryCode = translation.country_code;

        // Si c'est une traduction de base (source = default et has_base_translation = true)
        if (translation.has_base_translation && translation.translation_source === 'default') {
          organized.baseTranslations[languageCode] = {
            label: translation.label || '',
            description: translation.description || '',
            source: translation.translation_source,
            hasTranslation: true
          };
        }

        // Si c'est un override (has_override = true)
        if (translation.has_override) {
          const key = `${countryCode}-${languageCode}`;
          organized.overrides[key] = {
            label: translation.label || '',
            description: translation.description || '',
            source: translation.translation_source,
            countryCode: countryCode,
            languageCode: languageCode
          };
        }
      });

      setTranslationsData(organized);

    } catch (err) {
      console.error('❌ Erreur lors du chargement des traductions:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedType?.id) {
      loadTranslations();
    }
  }, [selectedType?.id]);

  useEffect(() => {
    loadCountriesForLanguages();
  }, []);

  // Fermer les dropdowns quand on clique ailleurs
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.country-selector')) {
        setOpenCountrySelectors({});
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  const languageNames = {
    'fr': 'Français',
    'de': 'Allemand',
    'it': 'Italien',
    'nl': 'Néerlandais'
  };

  const languagePlaceholders = {
    'fr': { label: 'Nom en français...', description: 'Description en français...' },
    'de': { label: 'Deutsche Übersetzung...', description: 'Beschreibung auf Deutsch...' },
    'it': { label: 'Traduzione italiana...', description: 'Descrizione in italiano...' },
    'nl': { label: 'Nederlandse vertaling...', description: 'Beschrijving in het Nederlands...' }
  };



  // Fonctions de gestion des overrides
  const toggleCountrySelector = (langCode) => {
    setOpenCountrySelectors(prev => ({
      ...prev,
      [langCode]: !prev[langCode]
    }));
  };

  const selectCountryForOverride = (langCode, countryCode) => {
    const key = `${countryCode}-${langCode}`;
    setSelectedOverrides(prev => ({
      ...prev,
      [key]: {
        countryCode,
        languageCode: langCode,
        label: '',
        description: '',
        isNew: true
      }
    }));
    setOpenCountrySelectors(prev => ({
      ...prev,
      [langCode]: false
    }));
  };

  const removeOverride = (key) => {
    setSelectedOverrides(prev => {
      const newOverrides = { ...prev };
      delete newOverrides[key];
      return newOverrides;
    });
  };

  const updateCountrySearch = (langCode, searchTerm) => {
    setCountrySearchTerms(prev => ({
      ...prev,
      [langCode]: searchTerm
    }));
  };

  const getFilteredCountries = (langCode) => {
    const searchTerm = countrySearchTerms[langCode] || '';
    const countriesForLang = availableCountriesForLanguage[langCode] || [];

    if (!searchTerm) return countriesForLang;

    return countriesForLang.filter(country =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.code.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  if (loading) {
    return (
      <div className="border border-gray-200 rounded-lg p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Chargement des traductions...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="border border-red-200 rounded-lg p-4 bg-red-50">
        <p className="text-red-700">Erreur: {error}</p>
        <button
          onClick={loadTranslations}
          className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section des traductions de base par langue */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-blue-50 px-4 py-3 border-b border-gray-200">
          <h5 className="font-medium text-gray-900">Traductions de base (par langue)</h5>
          <p className="text-sm text-gray-600 mt-1">Traductions universelles utilisées par défaut pour chaque langue</p>
        </div>
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase w-32">Langue</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Label</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase w-40">Overrides</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {Object.entries(translationsData.baseTranslations || {}).map(([langCode, translation]) => {
              // Récupérer les overrides existants pour cette langue
              const existingOverrides = Object.entries(translationsData.overrides || {})
                .filter(([key, override]) => override.languageCode === langCode)
                .concat(
                  Object.entries(selectedOverrides)
                    .filter(([key, override]) => override.languageCode === langCode)
                );

              return (
                <tr key={langCode} className={translation.hasTranslation ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-4 py-3 text-sm font-medium text-gray-900">
                    {languageNames[langCode] || langCode.toUpperCase()}
                  </td>
                  <td className="px-4 py-3">
                    <input
                      type="text"
                      defaultValue={translation?.label || ''}
                      placeholder={languagePlaceholders[langCode]?.label}
                      className={`w-full px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        translation.hasTranslation ? 'border-gray-300' : 'border-gray-200 bg-gray-50'
                      }`}
                    />
                  </td>
                  <td className="px-4 py-3">
                    <input
                      type="text"
                      defaultValue={translation?.description || ''}
                      placeholder={languagePlaceholders[langCode]?.description}
                      className={`w-full px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        translation.hasTranslation ? 'border-gray-300' : 'border-gray-200 bg-gray-50'
                      }`}
                    />
                  </td>
                  <td className="px-4 py-3">
                    <div className="space-y-2">
                      {/* Overrides existants */}
                      {existingOverrides.map(([key, override]) => (
                        <div key={key}>
                          {override.isNew ? (
                            // Nouveaux overrides avec inputs
                            <div className="space-y-2 p-3 bg-green-50 rounded border border-green-200">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm">
                                    {getCountryFlag(override.countryCode)}
                                  </span>
                                  <span className="text-xs font-medium text-gray-700">
                                    {override.countryCode}
                                  </span>
                                </div>
                                <button
                                  onClick={() => removeOverride(key)}
                                  className="text-red-500 hover:text-red-700 text-xs"
                                  title="Annuler cet override"
                                >
                                  ✕
                                </button>
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <input
                                  type="text"
                                  placeholder={`${getCountryFlag(override.countryCode)} Libellé`}
                                  className="w-full px-2 py-1 border border-green-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                  onChange={(e) => {
                                    setSelectedOverrides(prev => ({
                                      ...prev,
                                      [key]: { ...prev[key], label: e.target.value }
                                    }));
                                  }}
                                />
                                <input
                                  type="text"
                                  placeholder={`${getCountryFlag(override.countryCode)} Description`}
                                  className="w-full px-2 py-1 border border-green-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                  onChange={(e) => {
                                    setSelectedOverrides(prev => ({
                                      ...prev,
                                      [key]: { ...prev[key], description: e.target.value }
                                    }));
                                  }}
                                />
                              </div>
                            </div>
                          ) : (
                            // Overrides existants (affichage simple)
                            <div className="flex items-center space-x-2 p-2 bg-orange-50 rounded border border-orange-200">
                              <span className="text-sm">
                                {getCountryFlag(override.countryCode)}
                              </span>
                              <span className="text-xs font-medium text-gray-700">
                                {override.countryCode}
                              </span>
                              <button
                                onClick={() => removeOverride(key)}
                                className="text-red-500 hover:text-red-700 text-xs"
                                title="Supprimer cet override"
                              >
                                ✕
                              </button>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* Bouton d'ajout d'override */}
                      <div className="relative country-selector">
                        <button
                          onClick={() => toggleCountrySelector(langCode)}
                          className={`flex items-center space-x-1 px-2 py-1 text-xs border rounded transition-colors ${
                            openCountrySelectors[langCode]
                              ? 'bg-red-50 hover:bg-red-100 border-red-200 text-red-700'
                              : 'bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700'
                          }`}
                        >
                          <span>{openCountrySelectors[langCode] ? '×' : '+'}</span>
                          <span>{openCountrySelectors[langCode] ? 'Annuler' : 'Ajouter pays'}</span>
                        </button>

                        {/* Dropdown de sélection de pays */}
                        {openCountrySelectors[langCode] && (
                          <div className="absolute top-full left-0 mt-1 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden">
                            <div className="p-2 border-b border-gray-100">
                              <input
                                type="text"
                                placeholder="Rechercher un pays..."
                                value={countrySearchTerms[langCode] || ''}
                                onChange={(e) => updateCountrySearch(langCode, e.target.value)}
                                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                autoFocus
                              />
                            </div>
                            <div className="overflow-y-auto max-h-48">
                              {getFilteredCountries(langCode).length > 0 ? (
                                getFilteredCountries(langCode).map(country => (
                                  <button
                                    key={country.code}
                                    onClick={() => selectCountryForOverride(langCode, country.code)}
                                    className="w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-gray-50 text-left transition-colors"
                                  >
                                    <span className="text-lg">{country.flag}</span>
                                    <span className="font-medium text-gray-900">{country.code}</span>
                                    <span className="text-gray-600 truncate">{country.name}</span>
                                  </button>
                                ))
                              ) : (
                                <div className="px-3 py-2 text-sm text-gray-500 text-center">
                                  Aucun pays trouvé
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>


    </div>
  );
};

export default Events;
